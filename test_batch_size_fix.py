#!/usr/bin/env python3
"""
Test script to verify the batch size mismatch fix works correctly.
"""

import torch
import sys
import os

# Add the project root to the path
sys.path.append('/home/<USER>/FL_BackdoorBench')

from backfed.models.word_model import RNNLanguageModel, RNNClassifier

def test_batch_size_mismatch_fix():
    """Test that the model handles different batch sizes correctly."""
    
    # Create a simple LSTM model
    model = RNNLanguageModel(
        rnn_type='LSTM',
        ntoken=1000,  # vocab size
        ninp=200,     # embedding size
        nhid=200,     # hidden size
        nlayers=2,    # number of layers
        dropout=0.2
    )
    
    print("Testing batch size mismatch fix...")
    
    # Test with initial batch size of 64
    batch_size_1 = 64
    seq_len = 10
    input_1 = torch.randint(0, 1000, (batch_size_1, seq_len))
    
    print(f"Testing with batch size {batch_size_1}")
    output_1, hidden_1 = model(input_1)
    print(f"Output shape: {output_1.shape}")
    print(f"Hidden state shapes: {hidden_1[0].shape}, {hidden_1[1].shape}")
    
    # Test with different batch size of 36 (simulating last batch)
    batch_size_2 = 36
    input_2 = torch.randint(0, 1000, (batch_size_2, seq_len))
    
    print(f"\nTesting with batch size {batch_size_2} using previous hidden state")
    try:
        output_2, hidden_2 = model(input_2, hidden_1)
        print(f"✅ Success! Output shape: {output_2.shape}")
        print(f"Hidden state shapes: {hidden_2[0].shape}, {hidden_2[1].shape}")
        print("The model correctly handled the batch size mismatch!")
    except RuntimeError as e:
        print(f"❌ Error: {e}")
        return False
    
    # Test with another different batch size
    batch_size_3 = 128
    input_3 = torch.randint(0, 1000, (batch_size_3, seq_len))
    
    print(f"\nTesting with batch size {batch_size_3} using previous hidden state")
    try:
        output_3, hidden_3 = model(input_3, hidden_2)
        print(f"✅ Success! Output shape: {output_3.shape}")
        print(f"Hidden state shapes: {hidden_3[0].shape}, {hidden_3[1].shape}")
        print("The model correctly handled another batch size change!")
    except RuntimeError as e:
        print(f"❌ Error: {e}")
        return False
    
    return True

def test_rnn_classifier_fix():
    """Test that the RNNClassifier also handles different batch sizes correctly."""

    # Create a simple RNN classifier
    model = RNNClassifier(
        ntoken=1000,     # vocab size
        ninp=200,        # embedding size
        nhid=200,        # hidden size
        nlayers=2,       # number of layers
        dropout=0.1,
        num_classes=2    # binary classification
    )

    print("\nTesting RNNClassifier batch size mismatch fix...")

    # Test with initial batch size of 64
    batch_size_1 = 64
    seq_len = 10
    input_1 = torch.randint(0, 1000, (batch_size_1, seq_len))
    hidden_1 = model.init_hidden(batch_size_1)

    print(f"Testing with batch size {batch_size_1}")
    output_1, hidden_1 = model(input_1, hidden_1)
    print(f"Output shape: {output_1.shape}")

    # Test with different batch size of 36 (simulating last batch)
    batch_size_2 = 36
    input_2 = torch.randint(0, 1000, (batch_size_2, seq_len))

    print(f"Testing with batch size {batch_size_2} using previous hidden state")
    try:
        output_2, hidden_2 = model(input_2, hidden_1)
        print(f"✅ Success! Output shape: {output_2.shape}")
        print("RNNClassifier correctly handled the batch size mismatch!")
        return True
    except RuntimeError as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    success1 = test_batch_size_mismatch_fix()
    success2 = test_rnn_classifier_fix()

    if success1 and success2:
        print("\n🎉 All tests passed! The batch size mismatch fix is working correctly for both models.")
    else:
        print("\n💥 Some tests failed. The fix needs more work.")
