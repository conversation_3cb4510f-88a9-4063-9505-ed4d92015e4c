---
# *Default configuration to simplify experiment setup
defaults:
  - _self_
  # - atk_config: ??? # Define attack config in child config file
  - override hydra/job_logging: rich  # Use our custom rich logging

# *Aggregator
aggregator: unweighted_fedavg

# *Specify if the attack is enabled
no_attack: False

# *Training mode
mode: parallel # Choose from [parallel, sequential]

# *Debug mode (Only works for CV datasets)
debug: False
debug_fraction_data: 0.1 # Only use a portion of the data in debug mode

# *Resource configuration for each client
cuda_visible_devices: 1,2,4,5,6
num_cpus: 1
num_gpus: 0.5

# *Reproducibility
seed: 123456
deterministic: False

# *Simulation configuration
num_rounds: 600
num_clients: 100
num_clients_per_round: 10
federated_evaluation: False # Whether to perform federated evaluation (evaluate global model on client's validation set)
federated_val_split: 0.0 # Validation split for each client's dataset (should be > 0 if federated evaluation is True)

# *Dataset distribution configuration
dataset: ??? # CIFAR10, CIFAR100, TinyImageNet, MNIST
datapath: data
partitioner: dirichlet # Choose from [uniform, dirichlet]
alpha: 0.5 # Dirichlet distribution parameter
normalize: True # Normalize the dataset

# Model configuration
model: ???
pretrain_model_path: Null # A weight path (.pth) | "IMAGENET1K_V2" | Null. This model_path is loaded only if checkpoint is Null.
num_classes: ???

# *Test configuration
test_batch_size: 512
num_workers: 4 # Number of workers for dataloader
pin_memory: True
test_every: 1 # Test every x rounds

# *Default local training configuration (passed to clients during training)
client_config:
  # Training configuration
  dataset: ${dataset}
  local_epochs: 2
  batch_size: 64
  mixed_precision: True   # Use mixed precision and grad scaling during training
  timeout: Null # Timeout for each client training (in seconds)
  optimizer: sgd # Choose from the key in optimizer section below. Will be overridden to store DictConfig in client_optimizer_config
  lr: 0.1 
  weight_decay: 5e-4
  momentum: 0.9
  val_split: ${federated_val_split} # split of the training data to be used for validation
  seed: ${seed}  # Reproducibility
  deterministic: ${deterministic}
  training_mode: ${mode} # Choose from [parallel, sequential]

# *Setting to resume model training (weight and other parameters are loaded).
# *See the full list of parameters in the save_best_model function in server.py
# *Note: If checkpoint is "wandb", the weight will be loaded from wandb

#* Save checkpoints
save_model: False # Save the model to run directory in outputs folder
save_checkpoint: False # Save the model as checkpoints in checkpoints folder (corresponding to Dataset, Model, and Strategy)
save_model_rounds: [200, 400, 600, 800, 1000] # Save the model at the specified rounds

# *Resume from checkpoint
# There are 3 ways to resume:
# 1. checkpoint is a round number, the weight will be loaded from the checkpoints folder corresponding to Dataset, Model, and Strategy
# E.g: checkpoint: 200, the weight will be loaded from the path ./checkpoints/CIFAR10_ResNet18_unweighted_fedavg/model_200.pth
# 2. checkpoint is "wandb", the weight will be loaded from wandb
# 3. checkpoint is a weight path (.pth), the weight will be loaded from the path
# If checkpoint is Null, the weight will not be loaded
checkpoint: Null # A number (round) | weight path (.pth) | "wandb" (load from wandb) | Null

# *Logging configuration
save_logging: csv # Choose from [wandb, csv (save to table), both (wandb & csv), Null (no save logging)]
# Add a name tag to the run name --> Final run name: {name}_{name_tag}
name_tag: ""
# Add a directory tag to csv file and log file --> Final path: {dir_tag}/{dir_path}
dir_tag: ${eval:"'${dataset}_${aggregator}_noattack' if ${no_attack} else '${dataset}_${aggregator}_${atk_config.model_poison_method}(${atk_config.data_poison_method})'"}

wandb:
  entity: dthinh-forwork-vinuniversity
  project: paper_exp
  mode: online
  name: ??? # Automatically set during runtime with the format <dataset_strategy_attack_partition>
  save_model: False # Whether to save the model to wandb
  save_model_round: -1 # Default: Save model at last round

# *Visualization
plot_data_distribution: False
plot_client_selection: False
disable_progress_bar: False

########################################################
# *Aggregator configuration
aggregator_config:
  weighted_fedavg:
    _target_: backfed.servers.WeightedFedAvgServer
    eta: 1

  unweighted_fedavg:
    _target_: backfed.servers.UnweightedFedAvgServer
    eta: 0.1

  fedprox:
    _target_: backfed.servers.FedProxServer
    proximal_mu: 0.01

  foolsgold:
    _target_: backfed.servers.FoolsGoldServer
    confidence: 1

  coordinate_median:
    _target_: backfed.servers.CoordinateMedianServer

  geometric_median:
    _target_: backfed.servers.GeometricMedianServer

  trimmed_mean:
    _target_: backfed.servers.TrimmedMeanServer
    trim_ratio: 0.2

  norm_clipping:
    _target_: backfed.servers.NormClippingServer
    clipping_norm: 3

  robustlr:
    _target_: backfed.servers.RobustLRServer
    robustLR_threshold: 8.0
    eta: 0.1

  krum:
    _target_: backfed.servers.KrumServer
    eta: 0.1

  multi_krum:
    _target_: backfed.servers.MultiKrumServer
    oracle: True # If true, we assume the number of malicious clients is known
    eta: 0.1

  ad_multi_krum:
    _target_: backfed.servers.ADMultiKrumServer # Multi-krum initialized as AnomalyDetectionServer
    eta: 0.1

  weakdp:
    _target_: backfed.servers.WeakDPServer
    strategy: unweighted_fedavg
    std_dev: 0.025
    clipping_norm: 5

  deepsight:
    _target_: backfed.servers.DeepSightServer
    num_seeds: 3
    num_samples: 20000
    deepsight_batch_size: 1000
    deepsight_tau: 0.3333
    eta: 0.1

  flame:
    _target_: backfed.servers.FlameServer
    lamda: 0.001

  rflbat:
    _target_: backfed.servers.RFLBATServer
    eps1: 10.0
    eps2: 4.0
    save_plots: False
    eta: 0.1

  fldetector:
    _target_: backfed.servers.FLDetectorServer
    window_size: 10  # Size of sliding window for detection
    eta: 0.1

  fltrust:
    _target_: backfed.servers.FLTrustServer
    eta: 0.1

  flare:
    _target_: backfed.servers.FlareServer
    voting_threshold: 0.5
    eta: 0.1

  indicator:
    _target_: backfed.servers.IndicatorServer

########################################################
# *Optimizer configuration
client_optimizer_config:
  sgd: # Default for CIFAR10, CIFAR100, EMNIST, TinyImageNet
    _target_: torch.optim.SGD
    lr: ${client_config.lr}
    weight_decay: ${client_config.weight_decay}
    momentum: ${client_config.momentum}
  adam:
    _target_: torch.optim.Adam
    lr: ${client_config.lr}
    weight_decay: ${client_config.weight_decay}
  adamw:
    _target_: torch.optim.AdamW
    lr: ${client_config.lr}
    weight_decay: ${client_config.weight_decay}

########################################################
# *Hydra configuration
output_dir: outputs/${dir_tag}/${now:%Y-%m-%d-%H-%M-%S}
hydra:
  job:
    chdir: False  # Prevent Hydra from changing working directory
  run:
    dir: ./outputs/${dir_tag}/${now:%Y-%m-%d-%H-%M-%S}
  sweep:
    dir: ./multirun/${dir_tag}/${now:%Y-%m-%d-%H-%M-%S}
    subdir: ${hydra.job.num}
    # dir: ./multirun/${dir_tag}/${now:%Y-%m-%d-%H-%M-%S}/${hydra.job.override_dirname}
    # subdir: ${hydra.job.num}
