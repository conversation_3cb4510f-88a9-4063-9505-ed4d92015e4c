---
# *Default configuration to simplify experiment setup
defaults:
  - base # Inherit from base config
  - _self_ # This ensures your config overrides the base config
  - atk_config: emnist_multishot

# *Aggregator
aggregator: unweighted_fedavg
checkpoint: checkpoints/EMNIST_BYCLASS_unweighted_fedavg/mnistnet_round_1000_dir_0.5.pth

# *Specify if the attack is enabled
no_attack: False

# *Simulation configuration
num_rounds: 600
num_clients: 3383
num_clients_per_round: 30
federated_evaluation: False # Whether to perform federated evaluation (evaluate global model on client's validation set)
federated_val_split: 0.0 # Validation split for each client's dataset (should be > 0 if federated evaluation is True)

# *Dataset distribution configuration
dataset: EMNIST_BYCLASS # CIFAR10, CIFAR100, TinyImageNet, MNIST
datapath: data
task: classification
partitioner: dirichlet # Choose from [uniform, dirichlet]
alpha: 0.5 # Dirichlet distribution parameter
normalize: True # Normalize the dataset

# *Model configuration
model: MnistNet
num_classes: 62

# *Test configuration
test_batch_size: 2048
test_every: 1 # Test every x rounds

# *Default local training configuration (passed to clients during training)
client_config:
  # Training configuration
  local_epochs: 2
  batch_size: 64
  optimizer: sgd # Choose from the key in optimizer section below. Will be overridden to store DictConfig in client_optimizer_config
  lr: 0.1
  weight_decay: 5e-4
