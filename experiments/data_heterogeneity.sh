python main.py -m \
    aggregator=robustlr,norm_clipping,trimmed_mean,coordinate_median,geometric_median,krum \
    checkpoint=checkpoints/CIFAR10_unweighted_fedavg/ResNet18_round_2000_dir_0.5.pth \
    partitioner=uniform \
    atk_config=multishot \
    atk_config.data_poison_method=pattern \
    atk_config.poison_start_round=2301 \
    atk_config.poison_end_round=2600 \
    num_rounds=600 \
    save_logging=csv \
    num_gpus=1 \
    num_cpus=1 \
    cuda_visible_devices=\"0,1,2,4,5\" \
    dir_tag=robust_aggregation_multishot_uniform 


python main.py -m \
    aggregator=robustlr \
    checkpoint=2300 \
    partitioner=uniform \
    atk_config=multishot \
    atk_config.mutual_dataset=True \
    atk_config.data_poison_method=pattern \
    atk_config.poison_start_round=2301 \
    atk_config.poison_end_round=2600 \
    num_rounds=checkpoints/CIFAR10_robustlr/ResNet18_round_2300_dir_0.5.pth \
    save_logging=csv \
    num_gpus=1 \
    num_cpus=1 \
    cuda_visible_devices=\"0,1,2,4,5\" \
    dir_tag=robust_aggregation_multishot_uniform_mutual && \ 
python main.py -m \
    aggregator=norm_clipping \
    checkpoint=2300 \
    partitioner=uniform \
    atk_config=multishot \
    atk_config.mutual_dataset=True \
    atk_config.data_poison_method=pattern \
    atk_config.poison_start_round=2301 \
    atk_config.poison_end_round=2600 \
    num_rounds=checkpoints/CIFAR10_norm_clipping/ResNet18_round_2300_dir_0.5.pth \
    save_logging=csv \
    num_gpus=1 \
    num_cpus=1 \
    cuda_visible_devices=\"0,1,2,4,5\" \
    dir_tag=robust_aggregation_multishot_uniform_mutual && \ 
python main.py -m \
    aggregator=trimmed_mean \
    checkpoint=2300 \
    partitioner=uniform \
    atk_config=multishot \
    atk_config.mutual_dataset=True \
    atk_config.data_poison_method=pattern \
    atk_config.poison_start_round=2301 \
    atk_config.poison_end_round=2600 \
    num_rounds=checkpoints/CIFAR10_trimmed_mean/ResNet18_round_2300_dir_0.5.pth \
    save_logging=csv \
    num_gpus=1 \
    num_cpus=1 \
    cuda_visible_devices=\"0,1,2,4,5\" \
    dir_tag=robust_aggregation_multishot_uniform_mutual && \ 
python main.py -m \
    aggregator=foolsgold \
    checkpoint=2300 \
    partitioner=uniform \
    atk_config=multishot \
    atk_config.mutual_dataset=True \
    atk_config.data_poison_method=pattern \
    atk_config.poison_start_round=2301 \
    atk_config.poison_end_round=2600 \
    num_rounds=checkpoints/CIFAR10_foolsgold/ResNet18_round_2300_dir_0.5.pth \
    save_logging=csv \
    num_gpus=1 \
    num_cpus=1 \
    cuda_visible_devices=\"0,1,2,4,5\" \
    dir_tag=robust_aggregation_multishot_uniform_mutual && \ 
python main.py -m \
    aggregator=geometric_median \
    checkpoint=2300 \
    partitioner=uniform \
    atk_config=multishot \
    atk_config.mutual_dataset=True \
    atk_config.data_poison_method=pattern \
    atk_config.poison_start_round=2301 \
    atk_config.poison_end_round=2600 \
    num_rounds=checkpoints/CIFAR10_geometric_median/ResNet18_round_2300_dir_0.5.pth \
    save_logging=csv \
    num_gpus=1 \
    num_cpus=1 \
    cuda_visible_devices=\"0,1,2,4,5\" \
    dir_tag=robust_aggregation_multishot_uniform_mutual && \ 
python main.py -m \
    aggregator=coordinate_median \
    checkpoint=2300 \
    partitioner=uniform \
    atk_config=multishot \
    atk_config.mutual_dataset=True \
    atk_config.data_poison_method=pattern \
    atk_config.poison_start_round=2301 \
    atk_config.poison_end_round=2600 \
    num_rounds=checkpoints/CIFAR10_coordinate_median/ResNet18_round_2300_dir_0.5.pth \
    save_logging=csv \
    num_gpus=1 \
    num_cpus=1 \
    cuda_visible_devices=\"0,1,2,4,5\" \
    dir_tag=robust_aggregation_multishot_uniform_mutual && \ 
python main.py -m \
    aggregator=krum \
    checkpoint=2300 \
    partitioner=uniform \
    atk_config=multishot \
    atk_config.mutual_dataset=True \
    atk_config.data_poison_method=pattern \
    atk_config.poison_start_round=2301 \
    atk_config.poison_end_round=2600 \
    num_rounds=checkpoints/CIFAR10_krum/ResNet18_round_2300_dir_0.5.pth \
    save_logging=csv \
    num_gpus=1 \
    num_cpus=1 \
    cuda_visible_devices=\"0,1,2,4,5\" \
    dir_tag=robust_aggregation_multishot_uniform_mutual && \ 